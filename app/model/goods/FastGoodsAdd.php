<?php
/**
 * xiaoxia 二开  快速商品入库类
 * =========================================================
 */

namespace app\model\goods;

use app\model\BaseModel;

/**
 * 快速商品入库类
 * 无需API接口，可直接调用
 */
class FastGoodsAdd extends BaseModel
{
    /**
     * 添加商品的方法
     * @param array $params 商品数据
     * @param int $site_id 站点ID
     * @return array
     */
    public function addGoods($params = [], $site_id = 0)
    {
        // 构建商品数据
        $data = [
            'goods_name' => $params['goods_name'] ?? '',// 商品名称
            'goods_attr_class' => $params['goods_attr_class'] ?? 0,// 商品类型id
            'goods_attr_name' => $params['goods_attr_name'] ?? '',// 商品类型名称
            'site_id' => $site_id ?: ($params['site_id'] ?? 1),
            'category_id' => $params['category_id'] ?? '',
            'category_json' => $this->getCategoryJson($params),
            'goods_image' => $params['goods_image'] ?? '',// 商品主图路径
            'goods_content' => $params['goods_content'] ?? '',// 商品详情
            'goods_state' => $params['goods_state'] ?? 1,// 商品状态（1.销售中 0.仓库中）
            'price' => $params['price'] ?? 0,// 商品价格
            'market_price' => $params['market_price'] ?? 0,// 市场价格
            'cost_price' => $params['cost_price'] ?? 0,// 成本价
            'weight' => $params['weight'] ?? 0,// 重量
            'volume' => $params['volume'] ?? 0,// 体积
            'goods_stock' => $params['goods_stock'] ?? 0,// 商品库存
            'goods_stock_alarm' => $params['goods_stock_alarm'] ?? 0,// 库存预警
            'is_free_shipping' => $params['is_free_shipping'] ?? 0,// 是否免邮
            'shipping_template' => $params['shipping_template'] ?? 1,// 运费模板
            'introduction' => $params['introduction'] ?? '',// 促销语
            'keywords' => $params['keywords'] ?? '',// 关键词
            'unit' => $params['unit'] ?? '',// 单位
            'video_url' => $params['video_url'] ?? '',// 视频地址
            'sort' => $params['sort'] ?? 0,// 排序
            'virtual_sale' => $params['virtual_sale'] ?? rand(100,500),// 虚拟销量
            'max_buy' => $params['max_buy'] ?? 0,// 限购
            'min_buy' => $params['min_buy'] ?? 0,// 起售
            'brand_id' => $params['brand_id'] ?? 0,// 品牌id
            'brand_name' => $params['brand_name'] ?? '',// 品牌名称
            'goods_service_ids' => $params['goods_service_ids'] ?? '',// 商品服务
            'support_trade_type' => $params['support_trade_type'] ?? 'express,store,local',// 配送方式
            'sale_channel' => $params['sale_channel'] ?? 'all',// 销售渠道 all-全部 h5-手机端 pc-电脑端
            'sale_store' => $params['sale_store'] ?? 'all',// 销售门店
            'goods_spec_format' => $this->formatGoodsSpecFormat($params['goods_spec_format'] ?? ''),// 商品规格格式
            'goods_attr_format' => $params['goods_attr_format'] ?? '',// 商品属性格式
            'is_consume_discount' => $params['is_consume_discount'] ?? 1,// 是否参与会员折扣
            'label_id' => $params['label_id'] ?? 0,// 标签id
            'recommend_way' => $params['recommend_way'] ?? 0,// 推荐方式
            'is_limit' => $params['is_limit'] ?? 0,// 是否限时
            'limit_type' => $params['limit_type'] ?? 1,// 限时类型
            'timer_on' => $params['timer_on'] ?? 0,// 定时上架
            'timer_off' => $params['timer_off'] ?? 0,// 定时下架
            'sale_show' => $params['sale_show'] ?? 1,// 销量显示
            'stock_show' => $params['stock_show'] ?? 0,// 库存显示
            'market_price_show' => $params['market_price_show'] ?? 0,// 市场价显示
            'barrage_show' => $params['barrage_show'] ?? 1,// 购买人数显示
            'wb_spuId' => $params['goods_id'],// 外部商品id
        ];

        // 商品SKU数据
        $data['goods_sku_data'] = [];
        
        if (!empty($params['goods_sku_data'])) {
            // 如果传入了商品SKU数据，则处理SKU数据
            $sku_data = [];
            if (is_array($params['goods_sku_data'])) {
                foreach ($params['goods_sku_data'] as $sku) {
                    // 确保sku_spec_format是JSON字符串
                    if (isset($sku['sku_spec_format']) && is_string($sku['sku_spec_format'])) {
                        // 已经是JSON字符串，直接使用
                        $sku_item = $sku;
                    } else if (isset($sku['sku_spec_format']) && is_array($sku['sku_spec_format'])) {
                        // 是数组，转为JSON字符串
                        $sku_item = $sku;
                        $sku_item['sku_spec_format'] = json_encode($sku['sku_spec_format']);
                    } else {
                        // 无规格数据，创建空规格
                        $sku_item = $sku;
                        $sku_item['sku_spec_format'] = '';
                    }
                    $sku_data[] = $sku_item;
                }
                $data['goods_sku_data'] = json_encode($sku_data);
            } else if (is_string($params['goods_sku_data'])) {
                // 如果已经是JSON字符串，直接使用
                $data['goods_sku_data'] = $params['goods_sku_data'];
            }
        } else {

            // 如果没有传入SKU数据，则创建一个默认的SKU
            $data['goods_sku_data'] = json_encode([
                [
                    'spec_name' => '',
                    'sku_no' => $params['sku_no'] ?? '',
                    'sku_spec_format' => '',
                    'price' => $data['price'],
                    'market_price' => $data['market_price'],
                    'cost_price' => $data['cost_price'],
                    'stock' => $data['goods_stock'],
                    'stock_alarm' => $data['goods_stock_alarm'],
                    'weight' => $data['weight'],
                    'volume' => $data['volume'],
                    'sku_image' => $params['thumbnailImage'],
                    'sku_images' => $params['goods_image'],
                    'is_default' => 1,
                ]
            ]);
        }
        
        // 调用商品模型添加商品
        $goods_model = new Goods();
        $result = $goods_model->addGoods($data);
        
        return $result;
    }

    /**
     * 格式化商品分类JSON
     * 将逗号分隔的分类ID转换为正确的JSON格式
     * @param array|string $category_id 分类ID
     * @return string
     */
    private function formatCategoryJson($category_id)
    {
        // 如果已经是json格式，直接返回
        if (!empty($category_id) && is_string($category_id) && $this->isJson($category_id)) {
            return $category_id;
        }
        
        // 如果是字符串（逗号分隔），转为数组
        if (!empty($category_id) && is_string($category_id)) {
            $category_id = explode(',', $category_id);
        }
        
        // 确保是数组
        if (!is_array($category_id)) {
            $category_id = [];
        }
        
        // 构造符合系统要求的格式
        $result = [];
        foreach ($category_id as $index => $id) {
            if (!empty($id)) {
                $result[] = [$id];
            }
        }
        
        // 如果数组为空，至少添加一个空数组，避免json_decode出错
        if (empty($result)) {
            $result = [[]];
        }
        
        return json_encode($result);
    }
    
    /**
     * 判断字符串是否为JSON格式
     * @param string $string
     * @return boolean
     */
    private function isJson($string) {
        if (!is_string($string)) return false;
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }
    
    /**
     * 格式化商品规格格式
     * 确保goods_spec_format是有效的JSON字符串
     * @param string|array $goods_spec_format 商品规格格式
     * @return string
     */
    private function formatGoodsSpecFormat($goods_spec_format)
    {
        // 如果是空值，返回空字符串
        if (empty($goods_spec_format)) {
            return '';
        }
        
        // 如果已经是json格式字符串，直接返回
        if (is_string($goods_spec_format) && $this->isJson($goods_spec_format)) {
            return $goods_spec_format;
        }
        
        // 如果是数组，转为JSON
        if (is_array($goods_spec_format)) {
            return json_encode($goods_spec_format);
        }
        
        // 其他情况，尝试处理为有效JSON
        try {
            $decoded = json_decode($goods_spec_format, true);
            if (is_array($decoded)) {
                return json_encode($decoded);
            }
        } catch (\Exception $e) {
            // 处理异常情况
        }
        
        // 无法处理，返回空字符串
        return '';
    }

    /**
     * 获取分类JSON数据
     * 如果$params['category_json']有参数且是数组，就转成json
     * 如果为空或不是数组，就传入category_id组成
     * @param array $params 参数数组
     * @return string
     */
    private function getCategoryJson($params)
    {
        // 如果category_json存在且是数组，直接转为JSON
        if (isset($params['category_json']) && is_array($params['category_json'])) {
            return json_encode($params['category_json']);
        }

        // 如果category_json为空或不是数组，使用category_id生成
        return $this->formatCategoryJson($params['category_id'] ?? []);
    }
}

/**
 * 使用示例：
 * 
 * 创建单规格商品：
 * $fast_goods = new \app\model\goods\FastGoodsAdd();
 * $params = [
 *     'site_id' => 1,
 *     'goods_name' => '测试商品',
 *     'goods_image' => 'upload/common/default_goods_img.png',
 *     'price' => 100,
 *     'market_price' => 120,
 *     'cost_price' => 80,
 *     'goods_stock' => 100,
 *     'category_id' => '1,2,3',
 *     'goods_content' => '<p>商品详情</p>'
 * ];
 * $result = $fast_goods->addGoods($params);
 * 
 * 创建多规格商品：
 * $fast_goods = new \app\model\goods\FastGoodsAdd();
 * $params = [
 *     'site_id' => 1,
 *     'goods_name' => '测试规格商品',
 *     'goods_image' => 'upload/common/default_goods_img.png',
 *     'price' => 100,
 *     'goods_spec_format' => '[{"spec_id":1,"spec_name":"颜色","value":[{"spec_value_id":1,"spec_value_name":"红色"},{"spec_value_id":2,"spec_value_name":"蓝色"}]}]',
 *     'goods_sku_data' => [
 *         [
 *             'spec_name' => '红色',
 *             'sku_no' => 'SKU001',
 *             'sku_spec_format' => '[{"spec_id":1,"spec_name":"颜色","spec_value_id":1,"spec_value_name":"红色"}]',
 *             'price' => 100,
 *             'market_price' => 120,
 *             'cost_price' => 80,
 *             'stock' => 50,
 *             'stock_alarm' => 10,
 *             'weight' => 1,
 *             'volume' => 0,
 *             'sku_image' => 'upload/common/default_goods_img.png',
 *             'sku_images' => 'upload/common/default_goods_img.png',
 *             'is_default' => 1
 *         ],
 *         [
 *             'spec_name' => '蓝色',
 *             'sku_no' => 'SKU002',
 *             'sku_spec_format' => '[{"spec_id":1,"spec_name":"颜色","spec_value_id":2,"spec_value_name":"蓝色"}]',
 *             'price' => 110,
 *             'market_price' => 130,
 *             'cost_price' => 90,
 *             'stock' => 50,
 *             'stock_alarm' => 10,
 *             'weight' => 1,
 *             'volume' => 0,
 *             'sku_image' => 'upload/common/default_goods_img.png',
 *             'sku_images' => 'upload/common/default_goods_img.png',
 *             'is_default' => 0
 *         ]
 *     ]
 * ];
 * $result = $fast_goods->addGoods($params);
 */ 