<?php
/**
 * xiaoxia 二开  快速商品入库API
 * =========================================================
 */

namespace app\api\controller;

use app\model\goods\FastGoodsAdd;
use think\facade\Request;
use think\Response;

/**
 * 快速商品入库API
 * 
 * 接口说明：
 * 本接口用于快速添加商品，无需登录即可调用
 * 
 * 请求方式：POST
 * 请求URL：/api/fastgoods/add
 * 
 * 参数说明：
 * 1. 通用必填参数:
 *    - goods_name: 商品名称
 *    - category_id: 商品分类ID，多个分类用逗号分隔，如"1,2,3"
 *    - goods_image: 商品主图
 *    - goods_content: 商品详情
 *    - price: 商品价格（单规格商品必填）
 *    - market_price: 市场价
 *    - cost_price: 成本价
 *    - goods_stock: 商品库存（单规格商品必填）
 * 
 * 2. 单规格商品必填:
 *    - spec_type_status: 0（表示单规格）
 * 
 * 3. 多规格商品必填:
 *    - spec_type_status: 1（表示多规格）
 *    - goods_spec_format: 规格项格式，例如：
 *      [{"spec_id":-1,"spec_name":"颜色","value":[{"spec_id":-1,"spec_name":"颜色","spec_value_id":-1,"spec_value_name":"红色","image":""},{"spec_id":-1,"spec_name":"颜色","spec_value_id":-2,"spec_value_name":"蓝色","image":""}]}]
 *    - goods_sku_data: SKU数据，例如：
 *      [{"spec_name":"红色","sku_no":"SKU001","sku_spec_format":[{"spec_id":-1,"spec_name":"颜色","spec_value_id":-1,"spec_value_name":"红色","image":""}],"price":"100","market_price":"120","cost_price":"80","stock":"50","stock_alarm":"10","weight":"1","volume":"0","is_default":1},{"spec_name":"蓝色","sku_no":"SKU002","sku_spec_format":[{"spec_id":-1,"spec_name":"颜色","spec_value_id":-2,"spec_value_name":"蓝色","image":""}],"price":"105","market_price":"125","cost_price":"85","stock":"50","stock_alarm":"10","weight":"1","volume":"0","is_default":0}]
 * 
 * 4. 其他可选参数：
 *    - goods_attr_format: 商品属性，默认为"[]"
 *    - site_id: 站点ID，默认为1
 *    - goods_state: 商品状态，1为上架，0为下架，默认为1
 *    - timer_on: 定时上架时间戳，默认为0
 *    - timer_off: 定时下架时间戳，默认为0
 *    - is_free_shipping: 是否免邮，1为免邮，0为不免邮，默认为1
 *    - shipping_template: 运费模板ID，默认为0
 *    - goods_service_ids: 商品服务ID，多个用逗号分隔
 *    - virtual_sale: 虚拟销量，默认为0
 * 
 * 返回值说明：
 * - code: 0表示成功，其他表示失败
 * - message: 操作结果描述
 * - data: 成功时返回添加的商品ID
 * 
 * 使用示例请参考：
 * 1. 单规格商品: simple_sku_example.php
 * 2. 多规格商品: multi_sku_example.php
 */
class FastGoods
{
    /**
     * 站点ID
     * @var int
     */
    protected $site_id = 1;
    
    /**
     * 请求参数
     * @var array
     */
    protected $params = [];
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 处理请求参数
        $this->params = Request::param();
        
        // 获取站点ID
        $this->site_id = isset($this->params['site_id']) ? intval($this->params['site_id']) : 1;
    }
    
    /**
     * 无需登录的添加商品接口
     * @return mixed
     */
    public function add()
    {
        // 获取所有请求参数
        $params = $this->params;
        
        // 设置站点ID
        if (empty($params['site_id'])) {
            $params['site_id'] = $this->site_id;
        }
        
        // 调用FastGoodsAdd模型添加商品
        $fast_goods = new FastGoodsAdd();
        $result = $fast_goods->addGoods($params);
        
        return $this->response($result);
    }
    
    /**
     * 返回API响应数据
     * @param array $data 响应数据
     * @return \think\Response
     */
    protected function response($data)
    {
        $data['timestamp'] = time();
        return Response::create($data, 'json', 200);
    }
}

 